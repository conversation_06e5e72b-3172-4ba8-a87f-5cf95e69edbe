#!/usr/bin/env python3
import pandas as pd
import numpy as np

print("Arena Layout Analysis")
print("=" * 60)
print(f"Arena Dimensions:")
print(f"  X-axis: -9.1m to +10.2m (total width: 19.3m)")
print(f"  Y-axis: -4.42m to +5.5m (total depth: 9.92m)")
print(f"Workstation Dimensions: 1.0m × 0.6m")
print("=" * 60)

# Layout 1 (Excel)
try:
    print("\nLAYOUT 1 (from Excel file):")
    layout1_data = pd.read_excel('layout 1.xlsx')
    row = layout1_data.iloc[0]
    
    workstations = [
        ('AS_1', row['AS_1_neu_X'], row['AS_1_neu_Y'], row['AS_1_neu_Yaw']),
        ('AS_3', row['AS_3_neu_X'], row['AS_3_neu_Y'], row['AS_3_neu_Yaw']),
        ('AS_4', row['AS_4_neu_X'], row['AS_4_neu_Y'], row['AS_4_neu_Yaw']),
        ('AS_5', row['AS_5_neu_X'], row['AS_5_neu_Y'], row['AS_5_neu_Yaw']),
        ('AS_6', row['AS_6_neu_X'], row['AS_6_neu_Y'], row['AS_6_neu_Yaw'])
    ]
    
    for name, x, y, yaw in workstations:
        print(f"  {name}: Position ({x:6.2f}, {y:6.2f}) @ {np.degrees(yaw):6.1f}°")
        
except Exception as e:
    print(f"Error processing Layout 1: {e}")

# Layout 2 (CSV)
try:
    print("\nLAYOUT 2 (from CSV file):")
    layout2_data = pd.read_csv('layout2_positions.csv')
    row = layout2_data.iloc[0]
    
    workstations = [
        ('AS_1', row['AS_1_x'], row['AS_1_y'], row['AS_1_yaw']),
        ('AS_3', row['AS_3_x'], row['AS_3_y'], row['AS_3_yaw']),
        ('AS_4', row['AS_4_x'], row['AS_4_y'], row['AS_4_yaw']),
        ('AS_5', row['AS_5_x'], row['AS_5_y'], row['AS_5_yaw']),
        ('AS_6', row['AS_6_x'], row['AS_6_y'], row['AS_6_yaw'])
    ]
    
    for name, x, y, yaw in workstations:
        print(f"  {name}: Position ({x:6.2f}, {y:6.2f}) @ {np.degrees(yaw):6.1f}°")
        
except Exception as e:
    print(f"Error processing Layout 2: {e}")

# Layout 3 (CSV)
try:
    print("\nLAYOUT 3 (from CSV file):")
    layout3_data = pd.read_csv('layout3_positions.csv')
    row = layout3_data.iloc[0]
    
    workstations = [
        ('AS_1', row['AS_1_x'], row['AS_1_y'], row['AS_1_yaw']),
        ('AS_3', row['AS_3_x'], row['AS_3_y'], row['AS_3_yaw']),
        ('AS_4', row['AS_4_x'], row['AS_4_y'], row['AS_4_yaw']),
        ('AS_5', row['AS_5_x'], row['AS_5_y'], row['AS_5_yaw']),
        ('AS_6', row['AS_6_x'], row['AS_6_y'], row['AS_6_yaw']),
        ('KLT', row['KLT_x'], row['KLT_y'], row['KLT_yaw'])
    ]
    
    for name, x, y, yaw in workstations:
        print(f"  {name}: Position ({x:6.2f}, {y:6.2f}) @ {np.degrees(yaw):6.1f}°")
        
except Exception as e:
    print(f"Error processing Layout 3: {e}")

print("\n" + "=" * 60)
print("SVG visualizations have been created:")
print("  - layout1.svg")
print("  - layout2.svg") 
print("  - layout3.svg")
print("Open these files in a web browser to view the layouts.")
print("=" * 60)
