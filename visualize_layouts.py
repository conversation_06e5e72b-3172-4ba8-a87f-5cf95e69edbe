import matplotlib.pyplot as plt
import matplotlib.patches as patches
import pandas as pd
import numpy as np

# Arena dimensions
ARENA_X_MIN = -9.1
ARENA_X_MAX = 10.2
ARENA_Y_MIN = -4.42
ARENA_Y_MAX = 5.5

# Workstation dimensions
WS_WIDTH = 1.0
WS_HEIGHT = 0.6

def create_layout_visualization(layout_data, layout_name, ax):
    """Create visualization for a single layout"""
    
    # Draw arena boundaries
    arena_rect = patches.Rectangle(
        (ARENA_X_MIN, ARENA_Y_MIN), 
        ARENA_X_MAX - ARENA_X_MIN, 
        ARENA_Y_MAX - ARENA_Y_MIN,
        linewidth=2, edgecolor='black', facecolor='lightgray', alpha=0.3
    )
    ax.add_patch(arena_rect)
    
    # Extract workstation positions
    workstations = []
    
    if 'AS_1_neu_X' in layout_data:  # Layout 1 (Excel format)
        # Use the first row of data
        row = layout_data.iloc[0]
        workstations = [
            ('AS_1', row['AS_1_neu_X'], row['AS_1_neu_Y'], row['AS_1_neu_Yaw']),
            ('AS_3', row['AS_3_neu_X'], row['AS_3_neu_Y'], row['AS_3_neu_Yaw']),
            ('AS_4', row['AS_4_neu_X'], row['AS_4_neu_Y'], row['AS_4_neu_Yaw']),
            ('AS_5', row['AS_5_neu_X'], row['AS_5_neu_Y'], row['AS_5_neu_Yaw']),
            ('AS_6', row['AS_6_neu_X'], row['AS_6_neu_Y'], row['AS_6_neu_Yaw'])
        ]
    else:  # Layout 2 and 3 (CSV format)
        row = layout_data.iloc[0]
        workstations = [
            ('AS_1', row['AS_1_x'], row['AS_1_y'], row['AS_1_yaw']),
            ('AS_3', row['AS_3_x'], row['AS_3_y'], row['AS_3_yaw']),
            ('AS_4', row['AS_4_x'], row['AS_4_y'], row['AS_4_yaw']),
            ('AS_5', row['AS_5_x'], row['AS_5_y'], row['AS_5_yaw']),
            ('AS_6', row['AS_6_x'], row['AS_6_y'], row['AS_6_yaw'])
        ]
        
        # Add KLT if present (Layout 3)
        if 'KLT_x' in row:
            workstations.append(('KLT', row['KLT_x'], row['KLT_y'], row['KLT_yaw']))
    
    # Draw workstations
    colors = ['red', 'blue', 'green', 'orange', 'purple', 'brown']
    
    for i, (name, x, y, yaw) in enumerate(workstations):
        # Calculate workstation corners considering rotation
        cos_yaw = np.cos(yaw)
        sin_yaw = np.sin(yaw)
        
        # Workstation corners relative to center (before rotation)
        corners_rel = np.array([
            [-WS_WIDTH/2, -WS_HEIGHT/2],
            [WS_WIDTH/2, -WS_HEIGHT/2],
            [WS_WIDTH/2, WS_HEIGHT/2],
            [-WS_WIDTH/2, WS_HEIGHT/2]
        ])
        
        # Rotate corners
        rotation_matrix = np.array([[cos_yaw, -sin_yaw], [sin_yaw, cos_yaw]])
        corners_rotated = corners_rel @ rotation_matrix.T
        
        # Translate to actual position
        corners = corners_rotated + np.array([x, y])
        
        # Create workstation rectangle
        ws_rect = patches.Polygon(corners, closed=True, 
                                 facecolor=colors[i % len(colors)], 
                                 alpha=0.7, edgecolor='black', linewidth=1)
        ax.add_patch(ws_rect)
        
        # Add workstation label
        ax.text(x, y, name, ha='center', va='center', fontweight='bold', fontsize=8)
        
        # Draw orientation arrow
        arrow_length = 0.3
        arrow_x = x + arrow_length * cos_yaw
        arrow_y = y + arrow_length * sin_yaw
        ax.arrow(x, y, arrow_x - x, arrow_y - y, head_width=0.1, 
                head_length=0.1, fc='black', ec='black')
    
    # Set axis properties
    ax.set_xlim(ARENA_X_MIN - 1, ARENA_X_MAX + 1)
    ax.set_ylim(ARENA_Y_MIN - 1, ARENA_Y_MAX + 1)
    ax.set_aspect('equal')
    ax.grid(True, alpha=0.3)
    ax.set_xlabel('X (meters)')
    ax.set_ylabel('Y (meters)')
    ax.set_title(f'{layout_name}\nArena: {ARENA_X_MAX - ARENA_X_MIN:.1f}m × {ARENA_Y_MAX - ARENA_Y_MIN:.1f}m')

# Load data for all three layouts
print("Loading layout data...")

# Layout 1 (Excel)
layout1_data = pd.read_excel('layout 1.xlsx')

# Layout 2 (CSV)
layout2_data = pd.read_csv('layout2_positions.csv')

# Layout 3 (CSV)
layout3_data = pd.read_csv('layout3_positions.csv')

# Create visualizations
fig, axes = plt.subplots(1, 3, figsize=(18, 6))

create_layout_visualization(layout1_data, 'Layout 1', axes[0])
create_layout_visualization(layout2_data, 'Layout 2', axes[1])
create_layout_visualization(layout3_data, 'Layout 3', axes[2])

plt.tight_layout()
plt.savefig('arena_layouts_comparison.png', dpi=300, bbox_inches='tight')
plt.show()

print("Visualization saved as 'arena_layouts_comparison.png'")
