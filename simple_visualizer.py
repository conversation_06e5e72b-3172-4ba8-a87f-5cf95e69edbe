#!/usr/bin/env python3
import pandas as pd
import numpy as np

# Arena dimensions
ARENA_X_MIN = -9.1
ARENA_X_MAX = 10.2
ARENA_Y_MIN = -4.42
ARENA_Y_MAX = 5.5

# Workstation dimensions
WS_WIDTH = 1.0
WS_HEIGHT = 0.6

def create_svg_layout(layout_data, layout_name, filename):
    """Create SVG visualization for a single layout"""
    
    # SVG dimensions and scaling
    svg_width = 800
    svg_height = 400
    margin = 50
    
    # Calculate scaling factors
    arena_width = ARENA_X_MAX - ARENA_X_MIN
    arena_height = ARENA_Y_MAX - ARENA_Y_MIN
    
    scale_x = (svg_width - 2 * margin) / arena_width
    scale_y = (svg_height - 2 * margin) / arena_height
    scale = min(scale_x, scale_y)  # Use same scale for both axes to maintain aspect ratio
    
    # Center the arena in the SVG
    offset_x = margin + (svg_width - 2 * margin - arena_width * scale) / 2
    offset_y = margin + (svg_height - 2 * margin - arena_height * scale) / 2
    
    def transform_x(x):
        return offset_x + (x - ARENA_X_MIN) * scale
    
    def transform_y(y):
        return svg_height - (offset_y + (y - ARENA_Y_MIN) * scale)  # Flip Y axis for SVG
    
    # Start SVG
    svg_content = f'''<?xml version="1.0" encoding="UTF-8"?>
<svg width="{svg_width}" height="{svg_height}" xmlns="http://www.w3.org/2000/svg">
<title>{layout_name}</title>

<!-- Arena boundary -->
<rect x="{transform_x(ARENA_X_MIN)}" y="{transform_y(ARENA_Y_MAX)}" 
      width="{arena_width * scale}" height="{arena_height * scale}"
      fill="lightgray" fill-opacity="0.3" stroke="black" stroke-width="2"/>

<!-- Grid lines -->
'''
    
    # Add grid lines
    for x in range(int(ARENA_X_MIN), int(ARENA_X_MAX) + 1):
        svg_content += f'<line x1="{transform_x(x)}" y1="{transform_y(ARENA_Y_MIN)}" x2="{transform_x(x)}" y2="{transform_y(ARENA_Y_MAX)}" stroke="gray" stroke-width="0.5" opacity="0.5"/>\n'
    
    for y in range(int(ARENA_Y_MIN), int(ARENA_Y_MAX) + 1):
        svg_content += f'<line x1="{transform_x(ARENA_X_MIN)}" y1="{transform_y(y)}" x2="{transform_x(ARENA_X_MAX)}" y2="{transform_y(y)}" stroke="gray" stroke-width="0.5" opacity="0.5"/>\n'
    
    # Extract workstation positions
    workstations = []
    
    if 'AS_1_neu_X' in layout_data.columns:  # Layout 1 (Excel format)
        row = layout_data.iloc[0]
        workstations = [
            ('AS_1', row['AS_1_neu_X'], row['AS_1_neu_Y'], row['AS_1_neu_Yaw']),
            ('AS_3', row['AS_3_neu_X'], row['AS_3_neu_Y'], row['AS_3_neu_Yaw']),
            ('AS_4', row['AS_4_neu_X'], row['AS_4_neu_Y'], row['AS_4_neu_Yaw']),
            ('AS_5', row['AS_5_neu_X'], row['AS_5_neu_Y'], row['AS_5_neu_Yaw']),
            ('AS_6', row['AS_6_neu_X'], row['AS_6_neu_Y'], row['AS_6_neu_Yaw'])
        ]
    else:  # Layout 2 and 3 (CSV format)
        row = layout_data.iloc[0]
        workstations = [
            ('AS_1', row['AS_1_x'], row['AS_1_y'], row['AS_1_yaw']),
            ('AS_3', row['AS_3_x'], row['AS_3_y'], row['AS_3_yaw']),
            ('AS_4', row['AS_4_x'], row['AS_4_y'], row['AS_4_yaw']),
            ('AS_5', row['AS_5_x'], row['AS_5_y'], row['AS_5_yaw']),
            ('AS_6', row['AS_6_x'], row['AS_6_y'], row['AS_6_yaw'])
        ]
        
        # Add KLT if present (Layout 3)
        if 'KLT_x' in row:
            workstations.append(('KLT', row['KLT_x'], row['KLT_y'], row['KLT_yaw']))
    
    # Colors for workstations
    colors = ['red', 'blue', 'green', 'orange', 'purple', 'brown']
    
    # Draw workstations
    for i, (name, x, y, yaw) in enumerate(workstations):
        # Calculate workstation corners considering rotation
        cos_yaw = np.cos(yaw)
        sin_yaw = np.sin(yaw)
        
        # Workstation corners relative to center (before rotation)
        corners_rel = np.array([
            [-WS_WIDTH/2, -WS_HEIGHT/2],
            [WS_WIDTH/2, -WS_HEIGHT/2],
            [WS_WIDTH/2, WS_HEIGHT/2],
            [-WS_WIDTH/2, WS_HEIGHT/2]
        ])
        
        # Rotate corners
        rotation_matrix = np.array([[cos_yaw, -sin_yaw], [sin_yaw, cos_yaw]])
        corners_rotated = corners_rel @ rotation_matrix.T
        
        # Translate to actual position and transform to SVG coordinates
        corners = corners_rotated + np.array([x, y])
        
        # Create polygon points string
        points = []
        for corner in corners:
            points.append(f"{transform_x(corner[0])},{transform_y(corner[1])}")
        points_str = " ".join(points)
        
        # Add workstation rectangle
        color = colors[i % len(colors)]
        svg_content += f'<polygon points="{points_str}" fill="{color}" fill-opacity="0.7" stroke="black" stroke-width="1"/>\n'
        
        # Add workstation label
        text_x = transform_x(x)
        text_y = transform_y(y)
        svg_content += f'<text x="{text_x}" y="{text_y}" text-anchor="middle" dominant-baseline="middle" font-family="Arial" font-size="12" font-weight="bold">{name}</text>\n'
        
        # Draw orientation arrow
        arrow_length = 0.3
        arrow_end_x = x + arrow_length * cos_yaw
        arrow_end_y = y + arrow_length * sin_yaw
        
        svg_content += f'<line x1="{transform_x(x)}" y1="{transform_y(y)}" x2="{transform_x(arrow_end_x)}" y2="{transform_y(arrow_end_y)}" stroke="black" stroke-width="2" marker-end="url(#arrowhead)"/>\n'
    
    # Add arrow marker definition
    svg_content += '''
<!-- Arrow marker -->
<defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" 
            refX="9" refY="3.5" orient="auto">
        <polygon points="0 0, 10 3.5, 0 7" fill="black"/>
    </marker>
</defs>

<!-- Title and labels -->
<text x="400" y="30" text-anchor="middle" font-family="Arial" font-size="16" font-weight="bold">''' + layout_name + f'''</text>
<text x="400" y="50" text-anchor="middle" font-family="Arial" font-size="12">Arena: {arena_width:.1f}m × {arena_height:.1f}m | Workstation: {WS_WIDTH}m × {WS_HEIGHT}m</text>

<!-- Coordinate labels -->
<text x="{transform_x(ARENA_X_MIN)}" y="{transform_y(ARENA_Y_MIN) + 20}" text-anchor="middle" font-family="Arial" font-size="10">({ARENA_X_MIN}, {ARENA_Y_MIN})</text>
<text x="{transform_x(ARENA_X_MAX)}" y="{transform_y(ARENA_Y_MAX) - 10}" text-anchor="middle" font-family="Arial" font-size="10">({ARENA_X_MAX}, {ARENA_Y_MAX})</text>

</svg>'''
    
    # Save SVG file
    with open(filename, 'w') as f:
        f.write(svg_content)
    
    print(f"Created {filename}")
    
    # Print workstation positions
    print(f"\n{layout_name} Workstation Positions:")
    for name, x, y, yaw in workstations:
        print(f"  {name}: ({x:.2f}, {y:.2f}) @ {np.degrees(yaw):.1f}°")

# Load and process all layouts
print("Loading layout data...")
print("="*60)

try:
    # Layout 1 (Excel)
    layout1_data = pd.read_excel('layout 1.xlsx')
    create_svg_layout(layout1_data, 'Layout 1', 'layout1.svg')
    print()
except Exception as e:
    print(f"Error processing Layout 1: {e}")

try:
    # Layout 2 (CSV)
    layout2_data = pd.read_csv('layout2_positions.csv')
    create_svg_layout(layout2_data, 'Layout 2', 'layout2.svg')
    print()
except Exception as e:
    print(f"Error processing Layout 2: {e}")

try:
    # Layout 3 (CSV)
    layout3_data = pd.read_csv('layout3_positions.csv')
    create_svg_layout(layout3_data, 'Layout 3', 'layout3.svg')
    print()
except Exception as e:
    print(f"Error processing Layout 3: {e}")

print("="*60)
print("All layout visualizations created as SVG files!")
print("You can open the SVG files in any web browser to view the layouts.")
print("="*60)
